{"name": "axonCoreUI", "version": "0.0.1", "private": true, "homepage": ".", "dependencies": {"@hookform/resolvers": "^4.1.3", "@module-federation/utilities": "^3.1.51", "@reduxjs/toolkit": "^2.6.1", "ajv": "^8.17.1", "axon-core-api-sdk": "1.0.1039-dev", "axon-core-ui-shared": "^1.1.146-dev", "i18next": "^24.2.3", "js-cookie": "^3.0.5", "moment": "^2.30.1", "oidc-client-ts": "^3.2.0", "papaparse": "^5.5.2", "phlex-core-ui": "1.1.904", "react": "^18.3.1", "react-dom": "^18.3.1", "react-flagpack": "^2.0.6", "react-hook-form": "^7.55.0", "react-i18next": "^15.4.1", "react-markdown": "^10.1.0", "react-redux": "^9.2.0", "react-resizable-panels": "^2.1.7", "react-router": "^7.6.3", "react-router-dom": "^7.6.3", "react-syntax-highlighter": "^15.6.1", "react-toastify": "^11.0.5", "react-transition-group": "^4.4.5", "redux": "^5.0.1", "redux-persist": "^6.0.0", "styled-components": "^6.1.17", "yup": "^1.6.1"}, "devDependencies": {"@babel/core": "^7.26.10", "@babel/plugin-transform-runtime": "^7.26.10", "@babel/preset-env": "^7.26.9", "@babel/preset-react": "^7.26.3", "@babel/preset-typescript": "^7.27.0", "@babel/runtime": "^7.27.0", "@eslint/compat": "^1.2.8", "@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.24.0", "@progress/kendo-data-query": "^1.7.1", "@progress/kendo-drawing": "^1.21.2", "@progress/kendo-intl": "^3.1.2", "@progress/kendo-licensing": "^1.5.1", "@progress/kendo-popup-common": "^1.9.2", "@progress/kendo-react-animation": "^9.5.0", "@progress/kendo-react-buttons": "^9.5.0", "@progress/kendo-react-common": "^9.5.0", "@progress/kendo-react-data-tools": "^9.5.0", "@progress/kendo-react-dateinputs": "^9.5.0", "@progress/kendo-react-dialogs": "^9.5.0", "@progress/kendo-react-dropdowns": "^9.5.0", "@progress/kendo-react-excel-export": "^9.5.0", "@progress/kendo-react-grid": "^9.5.0", "@progress/kendo-react-inputs": "^9.5.0", "@progress/kendo-react-intl": "^9.5.0", "@progress/kendo-react-layout": "^9.5.0", "@progress/kendo-react-notification": "^9.5.0", "@progress/kendo-react-popup": "^9.5.0", "@progress/kendo-react-progressbars": "^9.5.0", "@progress/kendo-react-tooltip": "^9.5.0", "@progress/kendo-react-treeview": "^9.5.0", "@progress/kendo-react-upload": "^9.5.0", "@progress/kendo-svg-icons": "^4.0.0", "@progress/kendo-theme-default": "^10.5.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/copy-webpack-plugin": "^10.1.3", "@types/fork-ts-checker-webpack-plugin": "^0.4.5", "@types/jest": "^29.5.14", "@types/js-cookie": "^3.0.6", "@types/lodash": "^4.17.16", "@types/papaparse": "^5.3.15", "@types/react": "^18.3.20", "@types/react-dom": "^18.3.6", "@types/react-redux": "^7.1.34", "@types/react-router-dom": "^5.3.3", "@types/react-toastify": "^4.1.0", "@types/redux": "^3.6.31", "@types/styled-components": "5.1.34", "@types/webpack": "^5.28.5", "@types/webpack-dev-server": "^4.7.2", "@types/yup": "^0.32.0", "@typescript-eslint/eslint-plugin": "^8.29.1", "@typescript-eslint/parser": "^8.29.1", "babel-jest": "29.7.0", "babel-loader": "^10.0.0", "clean-webpack-plugin": "^4.0.0", "copy-webpack-plugin": "^13.0.0", "css-loader": "^7.1.2", "dotenv": "^16.4.7", "eslint": "^9.24.0", "eslint-config-prettier": "^10.1.1", "eslint-plugin-jest-dom": "^5.5.0", "eslint-plugin-prettier": "^5.2.6", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-testing-library": "^7.1.1", "eslint-webpack-plugin": "^5.0.0", "fork-ts-checker-webpack-plugin": "^9.1.0", "html-webpack-plugin": "^5.6.3", "jest": "29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-junit": "^16.0.0", "jest-localstorage-mock": "^2.4.26", "prettier": "^3.5.3", "style-loader": "^4.0.0", "ts-jest": "^29.3.1", "ts-node": "^10.9.2", "tsconfig-paths-webpack-plugin": "^4.2.0", "typescript": "^5.8.3", "webpack": "^5.99.2", "webpack-cli": "^6.0.1", "webpack-dev-server": "^5.2.1", "webpack-shell-plugin-next": "^2.3.2"}, "overrides": {"nanoid": "3.3.8", "prismjs": "1.30.0"}, "jest-junit": {"outputDirectory": "/testResults", "outputName": "test-report.xml"}, "scripts": {"start": "bash ./config/generate-ssl.sh && webpack serve --config webpack.dev.config.ts", "build": "webpack --config webpack.prod.config.ts", "test": "jest --passWithNoTests --env=jsdom", "test:coverage": "jest --passWithNoTests --coverage --env=jsdom", "test:ci": "jest --ci --reporters=default --reporters=jest-junit --passWithNoTests --env=jsdom", "lint": "eslint ./src ./__tests__", "lint-fix": "eslint ./src ./__tests__ --fix", "axon": "npm install && npm start"}}