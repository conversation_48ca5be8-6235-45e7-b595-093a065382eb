import React, { FC, useEffect, useState } from 'react';
import { <PERSON>rowser<PERSON>outer, Routes, Route, Navigate } from 'react-router-dom';
import { PhlexCardSkeleton, PhlexSharedThemeProvider, PhlexToastContainer } from 'phlex-core-ui';
import { Provider } from 'react-redux';
import { User } from 'oidc-client-ts';
import { clearAuthOnLogin, clearAuthOnLogout, getActiveUser, isAuthRedirect, setAuthAccount, setAuthProvider } from 'auth/services';
import { getOidcClient } from 'auth';
import { authenticationApiService } from 'api';
import { PersistGate } from 'redux-persist/integration/react';
import { routes, SignInPage, SignOutPage, ExternalLoginPage } from 'routes';
import { store } from './store';
import { ToastContainer } from 'react-toastify';
import { environmentVariableSrv } from 'shared/services';
import AuthenticatedApp from './AuthenticatedApp';
import GlobalStyles from './global-styles';
import '@progress/kendo-theme-default/dist/all.css';
import { getRedirectUrl, setStorageOnLoginRedirect } from 'shared/services/login-service';

const App: FC = () => {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  const onAuthRedirect = async () => {
    try {
      const user = await getOidcClient()?.signinCallback();
      history.replaceState(null, '', window.location.origin);
      await onUserLogin(user);
    } catch {
      setIsLoading(false);
    }
  };

  const onUserLogin = async (user: void | User | null) => {
    if (!user) {
      return;
    }
    await authenticationApiService
      .signIn(user.access_token, {
        withCredentials: true,
        headers: { Authorization: `Bearer ${user.id_token}` },
      })
      .then((response) => {
        if (response.data.data) {
          clearAuthOnLogin();
          sessionStorage.removeItem('phlex_loginSuccess');
          setAuthAccount(response.data.data);
          setIsAuthenticated(true);
          postLoginRedirect();
        } else {
          redirect('LOGIN_FAILED_FOR_USER_GENERIC');
        }
        setIsLoading(false);
      })
      .catch((error) => {
        setIsAuthenticated(false);
        clearAuthOnLogout();
        if (error?.status === 401) {
          redirect('LOGIN_FAILED_FOR_USER');
        } else {
          redirect('LOGIN_FAILED_FOR_USER_GENERIC');
        }
        setIsLoading(false);
      });
  };

  const postLoginRedirect = () => {
    const redirectUrl = getRedirectUrl();

    if (!redirectUrl) return;
    sessionStorage.removeItem('oidc.redirectUrl');
    window.location.href = `/${redirectUrl}`;
  };

  const redirect = (errorCode: string) => {
    window.location.href = `/sign-in?error=${errorCode}`;
  };

  //Determine if there is anything missing where we know
  //we would need to do a sign in. E.g user infomation
  const requiresSignIn = () => {
    const activeUser = getActiveUser();
    return activeUser === null;
  };

  useEffect(() => {
    setAuthProvider();

    const handleAuthState = async () => {
      if (environmentVariableSrv.getVariable('AXON_SHARED_LOCAL_TOKEN')) {
        setIsLoading(false);
        setIsAuthenticated(true);

        const user = environmentVariableSrv.getVariable('AXON_SHARED_LOCAL_USER');
        setAuthAccount(JSON.parse(user));
      }
      if (isAuthRedirect()) {
        await onAuthRedirect();
      } else if (requiresSignIn()) {
        setIsAuthenticated(false);
        setIsLoading(false);
        setStorageOnLoginRedirect(window.location);
      } else {
        //If we aren't an authentication request, and can't determine if we
        //aren't authenticated then assume we are. If a request throws a 401 it will
        //it will prompt the login flow.
        setIsAuthenticated(true);
        setIsLoading(false);
      }
    };
    handleAuthState();
  });

  return (
    <BrowserRouter>
      <PhlexSharedThemeProvider>
        <Provider store={store.store}>
          <GlobalStyles />

          {!isLoading && !isAuthenticated && (
            <Routes>
              <Route path={routes.signIn} element={<SignInPage />} />
              <Route path={routes.signOut} element={<SignOutPage />} />
              <Route path={routes.externalLogin()} element={<ExternalLoginPage />} />
              <Route path={routes.externalLoginWithEnv()} element={<ExternalLoginPage />} />
              <Route path="/*" element={<Navigate to={routes.signIn} />} />
            </Routes>
          )}

          {isLoading && !isAuthenticated && <PhlexCardSkeleton showNav />}

          {isAuthenticated && !isLoading && (
            <PersistGate persistor={store.persistor}>
              <AuthenticatedApp />
            </PersistGate>
          )}

          <PhlexToastContainer />
          <ToastContainer hideProgressBar />
        </Provider>
      </PhlexSharedThemeProvider>
    </BrowserRouter>
  );
};

export default App;
