import React, { FunctionComponent, useEffect, useMemo, useState } from 'react';
import { useParams, useSearchParams, useNavigate } from 'react-router-dom';
import { FormProvider, useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { User } from 'oidc-client-ts';
import { toast } from 'react-toastify';

import { clearStorageOnLogin, setStorageOnLogin } from 'shared/services/login-service';
import { getOidcClient, setOidcClient } from 'auth';
import { clearAuthOnLogin, setAuthAccount } from 'auth/services';
import { authenticationApiService, userIdentityProviderApiService, organisationApiService, userAccessApiService } from 'api';
import { routes } from 'routes';
import StyledComponents from '../SignIn/SignInPage.styles'; // Reuse existing styles

type FormValues = { email: string };

interface ExternalLoginParams {
  organisation: string;
  appCodeName: string;
  env?: string;
}

const ExternalLoginPage: FunctionComponent = () => {
  const { t } = useTranslation();
  const { Wrapper, Form, Logo, Message, InactivityText, LoginInput, LoginButton } = StyledComponents;
  const { organisation, appCodeName, env } = useParams<ExternalLoginParams>();
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  
  const [isLoading, setIsLoading] = useState(false);
  const [userInput, setUserInput] = useState('');
  const [loadedError, setLoadedError] = useState('');
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [user, setUser] = useState<User | null>(null);
  
  const postLoginRedirect = searchParams.get('postLoginRedirect');
  const buttonLabel = useMemo(() => (isLoading ? t('Login.SigningIn') : t('Login.NextButton')), [isLoading, t]);

  // Validation schema
  const SignInSchema = yup.object().shape({
    email: yup
      .string()
      .required(t('Forms.RequiredField', { field: t('Login.EmailLabel') }))
      .email(t('Forms.InvalidEmail')),
  });

  const methods = useForm<FormValues>({
    resolver: yupResolver(SignInSchema),
  });

  // Check if user is already authenticated
  useEffect(() => {
    const checkAuthState = async () => {
      try {
        // Check if we're returning from OIDC redirect
        const isOidcRedirect = sessionStorage.getItem('oidc.redirect') === 'true';
        if (isOidcRedirect) {
          const oidcUser = await getOidcClient()?.signinCallback();
          if (oidcUser) {
            setUser(oidcUser);
            await handleUserAuthentication(oidcUser);
          }
        }
      } catch (error) {
        console.error('Auth state check failed:', error);
        setLoadedError(t('Login.UnableToLogIn'));
      }
    };

    checkAuthState();
  }, []);

  const handleUserAuthentication = async (oidcUser: User) => {
    try {
      setIsLoading(true);
      
      // Authenticate with Axon Core API
      const response = await authenticationApiService.signIn(oidcUser.access_token, {
        withCredentials: true,
        headers: { Authorization: `Bearer ${oidcUser.id_token}` },
      });

      if (response.data.data) {
        clearAuthOnLogin();
        sessionStorage.removeItem('phlex_loginSuccess');
        setAuthAccount(response.data.data);
        setIsAuthenticated(true);
        
        // Validate organization access
        await validateOrganizationAccess(response.data.data);
      } else {
        throw new Error('Authentication failed');
      }
    } catch (error: any) {
      setIsAuthenticated(false);
      if (error?.status === 401) {
        setLoadedError(t('Login.UnableToLogIn'));
      } else if (error?.status === 403) {
        // User doesn't have access to the organization
        navigate(routes.forbidden);
        return;
      } else {
        setLoadedError(t('Login.UnableToLogIn'));
      }
    } finally {
      setIsLoading(false);
    }
  };

  const validateOrganizationAccess = async (userData: any) => {
    try {
      if (!organisation || !appCodeName) {
        throw new Error('Organization and app parameters are required');
      }

      // Check if user has access to the organization and app
      // Use basic permissions check - if user can access the app in the org, they should be able to use external login
      const accessResponse = await userAccessApiService.validateCurrentUserPermissionsAccess({
        permissions: ['ViewApp'], // Basic permission to view apps
        appCodeName: appCodeName,
        orgCodeName: organisation,
      });

      if (!accessResponse.data.data || !accessResponse.data.data.permissions || accessResponse.status === 403) {
        // User doesn't have access to this organization/app combination
        navigate(routes.forbidden);
        return;
      }

      // If we reach here, user has access - generate one-time code and redirect
      await generateCodeAndRedirect();

    } catch (error: any) {
      if (error?.status === 403) {
        navigate(routes.forbidden);
      } else {
        setLoadedError(t('Shared.ApiFailedErrorMessage'));
      }
    }
  };

  const generateCodeAndRedirect = async () => {
    try {
      if (!organisation || !appCodeName) {
        throw new Error('Required parameters missing');
      }

      // Generate one-time code using existing API
      // Note: The environment parameter may need to be added to the API if not already supported
      const codeResponse = await organisationApiService.issueCode(organisation, {
        appCodeName
        // TODO: Add environment parameter support if needed in the API
        // ...(env && { environment: env })
      });

      if (codeResponse.status === 200 && codeResponse.data?.data?.code) {
        const code = codeResponse.data.data.code;

        // Construct the redirect URL to external application's axon-auth/sign-in endpoint
        let redirectUrl = `axon-auth/sign-in?code=${code}`;

        if (env) {
          redirectUrl += `&env=${env}`;
        }

        if (postLoginRedirect) {
          redirectUrl += `&postLoginRedirect=${encodeURIComponent(postLoginRedirect)}`;
        }

        // Redirect to external application
        window.location.href = redirectUrl;
      } else {
        throw new Error('Failed to generate one-time code');
      }
    } catch (error) {
      console.error('Code generation failed:', error);
      setLoadedError(t('Shared.ApiFailedErrorMessage'));
    }
  };

  const onSubmit = async (e: React.MouseEvent<HTMLButtonElement, MouseEvent>) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const response = await userIdentityProviderApiService.getLoginProviderDetails(userInput);
      const data = response.data.data;

      const provider = {
        authority: data?.authority ?? '',
        client_id: data?.clientId ?? '',
        custom_refresh: data?.useCustomRefresh ?? false,
      };

      setOidcClient(provider);
      setStorageOnLogin(provider);

      // Store external login context for post-auth processing
      sessionStorage.setItem('externalLogin.organisation', organisation || '');
      sessionStorage.setItem('externalLogin.appCodeName', appCodeName || '');
      if (env) {
        sessionStorage.setItem('externalLogin.env', env);
      }
      if (postLoginRedirect) {
        sessionStorage.setItem('externalLogin.postLoginRedirect', postLoginRedirect);
      }

      getOidcClient()?.signinRedirect({ login_hint: userInput });
    } catch {
      setIsLoading(false);
      toast.error(t('Login.UnableToLogIn'));
    } finally {
      clearStorageOnLogin();
    }
  };

  // If user is authenticated and validated, show loading while redirecting
  if (isAuthenticated) {
    return (
      <Wrapper>
        <Form>
          <Logo src="assets/login-logo.png" alt={t('Login.LogoAlt')} />
          <Message>{t('Login.RedirectingToApp', 'Redirecting to application...')}</Message>
        </Form>
      </Wrapper>
    );
  }

  return (
    <Wrapper>
      <FormProvider {...methods}>
        <Form data-testid="external-signin-form">
          <Logo src="assets/login-logo.png" alt={t('Login.LogoAlt')} />
          <Message>{t('Login.LoginToSmartPhlex')}</Message>

          {loadedError && <InactivityText color="amber">{loadedError}</InactivityText>}
          
          <LoginInput
            label={t('Login.EmailLabel')}
            name="email"
            onChange={(e) => setUserInput(e.value)}
            type="email"
            value={userInput}
            width="fullwidth"
          />
          <LoginButton
            fullwidth={true}
            label={buttonLabel}
            disabled={!methods.formState.isValid || isLoading}
            onClick={async (e) => await onSubmit(e)}
          />
        </Form>
      </FormProvider>
    </Wrapper>
  );
};

export default ExternalLoginPage;
