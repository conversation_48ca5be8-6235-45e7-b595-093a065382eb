import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import { I18nextProvider } from 'react-i18next';
import { PhlexSharedThemeProvider } from 'phlex-core-ui';
import i18n from 'i18next';

import ExternalLoginPage from '../ExternalLoginPage';
import { authenticationApiService, userIdentityProviderApiService, organisationApiService, userAccessApiService } from 'api';

// Mock the API services
jest.mock('api', () => ({
  authenticationApiService: {
    signIn: jest.fn(),
  },
  userIdentityProviderApiService: {
    getLoginProviderDetails: jest.fn(),
  },
  organisationApiService: {
    issueCode: jest.fn(),
  },
  userAccessApiService: {
    validateCurrentUserPermissionsAccess: jest.fn(),
  },
}));

// Mock auth services
jest.mock('auth/services', () => ({
  clearAuthOnLogin: jest.fn(),
  setAuthAccount: jest.fn(),
}));

// Mock auth OIDC
const mockSigninCallback = jest.fn();
const mockSigninRedirect = jest.fn();
const mockOidcClient = {
  signinCallback: mockSigninCallback,
  signinRedirect: mockSigninRedirect,
};
const mockGetOidcClient = jest.fn(() => mockOidcClient);

jest.mock('auth', () => ({
  getOidcClient: mockGetOidcClient,
  setOidcClient: jest.fn(),
}));

// Mock login service
jest.mock('shared/services/login-service', () => ({
  clearStorageOnLogin: jest.fn(),
  setStorageOnLogin: jest.fn(),
}));

// Mock react-router-dom
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useParams: () => ({
    organisation: 'test-org',
    appCodeName: 'test-app',
    env: 'dev',
  }),
  useSearchParams: () => [
    new URLSearchParams('postLoginRedirect=https://example.com/redirect'),
  ],
  useNavigate: () => mockNavigate,
}));

// Mock window.location
Object.defineProperty(window, 'location', {
  value: {
    href: '',
  },
  writable: true,
});

// Initialize i18n for testing
i18n.init({
  lng: 'en',
  resources: {
    en: {
      translation: {
        'Login.EmailLabel': 'Email',
        'Login.NextButton': 'Next',
        'Login.SigningIn': 'Signing in...',
        'Login.LoginToSmartPhlex': 'Login to SmartPhlex',
        'Login.UnableToLogIn': 'Unable to log in',
        'Login.LogoAlt': 'SmartPhlex Logo',
        'Forms.RequiredField': '{{field}} is required',
        'Forms.InvalidEmail': 'Invalid email format',
        'Shared.ApiFailedErrorMessage': 'API request failed',
      },
    },
  },
});

// Create a mock store
const createMockStore = () => {
  return configureStore({
    reducer: {
      auth: (state = { isLoading: false, permissions: null, organisationAppPermissions: [] }) => state,
      global: (state = { organisations: [], apps: [], userOrganisation: { codeName: 'test' } }) => state,
    },
  });
};

const renderWithProviders = (component: React.ReactElement) => {
  const store = createMockStore();
  return render(
    <Provider store={store}>
      <I18nextProvider i18n={i18n}>
        <PhlexSharedThemeProvider>
          <BrowserRouter>
            {component}
          </BrowserRouter>
        </PhlexSharedThemeProvider>
      </I18nextProvider>
    </Provider>
  );
};

describe('ExternalLoginPage', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    sessionStorage.clear();
    mockSigninCallback.mockClear();
    mockSigninRedirect.mockClear();
    mockGetOidcClient.mockClear();
    mockGetOidcClient.mockReturnValue(mockOidcClient);
    window.location.href = '';
  });

  it('should render the external login form', () => {
    renderWithProviders(<ExternalLoginPage />);

    expect(screen.getByTestId('external-signin-form')).toBeInTheDocument();
    expect(screen.getByText('Login to SmartPhlex')).toBeInTheDocument();
    expect(screen.getByTestId('phlex-input-email')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'Next' })).toBeInTheDocument();
  });

  it('should validate email input', async () => {
    renderWithProviders(<ExternalLoginPage />);

    const emailInput = screen.getByTestId('phlex-input-email');
    const submitButton = screen.getByRole('button', { name: 'Next' });

    // Button should be disabled initially
    expect(submitButton).toBeDisabled();

    // Enter invalid email
    fireEvent.change(emailInput, { target: { value: 'invalid-email' } });
    expect(submitButton).toBeDisabled();

    // Enter valid email
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    await waitFor(() => {
      expect(submitButton).not.toBeDisabled();
    });
  });

  it('should handle successful authentication flow', async () => {
    // Mock successful API responses
    (userIdentityProviderApiService.getLoginProviderDetails as jest.Mock).mockResolvedValue({
      data: {
        data: {
          authority: 'https://auth.example.com',
          clientId: 'test-client-id',
          useCustomRefresh: false,
        },
      },
    });

    renderWithProviders(<ExternalLoginPage />);

    const emailInput = screen.getByTestId('phlex-input-email');
    const submitButton = screen.getByRole('button', { name: 'Next' });

    // Wait for form to be ready
    await waitFor(() => {
      expect(submitButton).toBeInTheDocument();
    });

    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });

    // Wait for validation to complete
    await waitFor(() => {
      expect(submitButton).not.toBeDisabled();
    });

    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(userIdentityProviderApiService.getLoginProviderDetails).toHaveBeenCalledWith('<EMAIL>');
    });
  });

  it('should handle organization access validation failure', async () => {
    // Mock authentication success but access validation failure
    (authenticationApiService.signIn as jest.Mock).mockResolvedValue({
      data: {
        data: {
          email: '<EMAIL>',
          userName: 'Test User',
        },
      },
    });

    (userAccessApiService.validateCurrentUserPermissionsAccess as jest.Mock).mockRejectedValue({
      status: 403,
    });

    // Mock OIDC user for signin callback
    const mockUser = {
      access_token: 'mock-access-token',
      id_token: 'mock-id-token',
    };
    mockSigninCallback.mockResolvedValue(mockUser);

    // Simulate OIDC redirect scenario
    sessionStorage.setItem('oidc.redirect', 'true');

    renderWithProviders(<ExternalLoginPage />);

    await waitFor(() => {
      expect(mockNavigate).toHaveBeenCalledWith('/forbidden');
    });
  });

  it('should handle code generation and redirect', async () => {
    // Mock successful authentication and access validation
    (authenticationApiService.signIn as jest.Mock).mockResolvedValue({
      data: {
        data: {
          email: '<EMAIL>',
          userName: 'Test User',
        },
      },
    });

    (userAccessApiService.validateCurrentUserPermissionsAccess as jest.Mock).mockResolvedValue({
      data: {
        data: {
          permissions: { ViewApp: true },
        },
      },
      status: 200,
    });

    (organisationApiService.issueCode as jest.Mock).mockResolvedValue({
      status: 200,
      data: {
        data: {
          code: 'test-code-123',
        },
      },
    });

    // Mock OIDC user for signin callback
    const mockUser = {
      access_token: 'mock-access-token',
      id_token: 'mock-id-token',
    };
    mockSigninCallback.mockResolvedValue(mockUser);

    // Simulate OIDC redirect scenario
    sessionStorage.setItem('oidc.redirect', 'true');

    renderWithProviders(<ExternalLoginPage />);

    await waitFor(() => {
      expect(organisationApiService.issueCode).toHaveBeenCalledWith('test-org', {
        appCodeName: 'test-app',
      });
    });

    await waitFor(() => {
      expect(window.location.href).toBe('axon-auth/sign-in?code=test-code-123&env=dev&postLoginRedirect=https%3A%2F%2Fexample.com%2Fredirect');
    });
  });

  it('should handle API errors gracefully', async () => {
    const mockToastError = jest.fn();
    jest.mock('react-toastify', () => ({
      toast: {
        error: mockToastError,
      },
    }));

    (userIdentityProviderApiService.getLoginProviderDetails as jest.Mock).mockRejectedValue(
      new Error('API Error')
    );

    renderWithProviders(<ExternalLoginPage />);

    const emailInput = screen.getByTestId('phlex-input-email');
    const submitButton = screen.getByRole('button', { name: 'Next' });

    // Wait for form to be ready
    await waitFor(() => {
      expect(submitButton).toBeInTheDocument();
    });

    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });

    // Wait for validation to complete
    await waitFor(() => {
      expect(submitButton).not.toBeDisabled();
    });

    fireEvent.click(submitButton);

    // The error is handled by showing a toast, not by displaying text in the DOM
    await waitFor(() => {
      expect(userIdentityProviderApiService.getLoginProviderDetails).toHaveBeenCalledWith('<EMAIL>');
    });
  });

  it('should store external login context in session storage', async () => {
    (userIdentityProviderApiService.getLoginProviderDetails as jest.Mock).mockResolvedValue({
      data: {
        data: {
          authority: 'https://auth.example.com',
          clientId: 'test-client-id',
          useCustomRefresh: false,
        },
      },
    });

    renderWithProviders(<ExternalLoginPage />);

    const emailInput = screen.getByTestId('phlex-input-email');
    const submitButton = screen.getByRole('button', { name: 'Next' });

    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(sessionStorage.getItem('externalLogin.organisation')).toBe('test-org');
      expect(sessionStorage.getItem('externalLogin.appCodeName')).toBe('test-app');
      expect(sessionStorage.getItem('externalLogin.env')).toBe('dev');
      expect(sessionStorage.getItem('externalLogin.postLoginRedirect')).toBe('https://example.com/redirect');
    });
  });
});
