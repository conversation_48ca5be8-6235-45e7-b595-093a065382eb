export const routes = {
  home: '/',
  signIn: '/sign-in',
  signOut: '/sign-out',
  forbidden: '/forbidden',
  apps: '/apps',
  notFound: '/404',
  organisationDisabled: '/organisation-disabled',
  organisation: (codeName = ':codeName'): string => `/organisations/${codeName}/apps`,
  organisations: '/organisations',
  createOrganisation: '/create-organisation',
  createOrganisationApps: '/create-organisation/apps',
  editOrganisationApps: (codeName = ':codeName'): string => `/edit-organisation/${codeName}/apps`,
  myOrganisation: (codeName = ':codeName'): string => `/${codeName}`,
  myOrganisationApps: (codeName = ':codeName'): string => `/${codeName}/my-apps`,
  myApp: (codeName = ':codeName', appCodeName = ':appCodeName'): string => `/${codeName}/my-apps/${appCodeName}/`,
  manageApp: (codeName = ':codeName'): string => `/manageApp/${codeName}`,
  createApp: '/create-app',
  manageOrganisation: (codeName = ':codeName'): string => `/organisations/${codeName}/settings`,
  organisationUsers: (codeName = ':codeName'): string => `/organisations/${codeName}/users`,
  manageAppAccess: (codeName = ':codeName', appCodeName = ':appCodeName'): string => `/${codeName}/manage-app-access/${appCodeName}/`,
  manageAxonAccess: (codeName = ':codeName'): string => `/organisations/${codeName}/access`,
  orgAppSettings: (codeName = ':codeName', appCodeName = ':appCodeName'): string => `/${codeName}/${appCodeName}/settings`,
  orgAuditLog: (codeName = ':codeName', appCodeName = ':appCodeName'): string => `/organisations/${codeName}/logs/${appCodeName}`,
  appRedirect: (codeName = ':codeName', appCodeName = ':appCodeName'): string => `redirect/${codeName}/my-apps/${appCodeName}/`,
  appActions: (codeName = ':codeName', appCodeName = ':appCodeName'): string => `/${codeName}/${appCodeName}/app-actions`,
  externalLogin: (organisation = ':organisation', appCodeName = ':appCodeName'): string => `/${organisation}/externalLogin/${appCodeName}`,
  externalLoginWithEnv: (organisation = ':organisation', appCodeName = ':appCodeName', env = ':env'): string => `/${organisation}/externalLogin/${appCodeName}/${env}`,
};

export const APP_ACTIONS_NESTED_ROUTES = {
  APP_SETTINGS: 'app-settings',
  MANAGE_ACCESS: 'manage-access',
  AUDIT_LOG: 'audit-log',
  ADVANCED: 'advanced',
};
